<?php

namespace Tests\Unit\Services;

use App\Models\Field;
use App\Services\ReservationCostService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(ReservationCostService::class)]
class DayNightRateCalculationTest extends TestCase
{
    use RefreshDatabase;

    private Field $field;

    private ReservationCostService $service;

    protected function setUp(): void
    {
        parent::setUp();

        $this->service = new ReservationCostService;

        // Create a test field with day/night rates
        $this->field = Field::factory()->create([
            'name' => 'Test Field',
            'hourly_rate' => 35.00,
            'night_hourly_rate' => 50.00,
            'night_time_start' => '18:00',
            'status' => 'Active',
        ]);
    }

    #[Test]
    public function it_calculates_cost_correctly_for_day_time_only()
    {
        // 2:00 PM to 4:00 PM (2 hours, all day rate)
        $cost = $this->service->calculateTotalCost($this->field, 2, '14:00');

        $this->assertEquals(70.00, $cost); // 35 * 2
    }

    #[Test]
    public function it_calculates_cost_correctly_for_night_time_only()
    {
        // 8:00 PM to 10:00 PM (2 hours, all night rate)
        $cost = $this->service->calculateTotalCost($this->field, 2, '20:00');

        $this->assertEquals(100.00, $cost); // 50 * 2
    }

    #[Test]
    public function it_calculates_cost_correctly_for_day_night_transition()
    {
        // 4:00 PM to 8:00 PM (4 hours, crosses 6PM night start)
        $cost = $this->service->calculateTotalCost($this->field, 4, '16:00');

        // 2 hours day rate (4PM-6PM) + 2 hours night rate (6PM-8PM)
        $expected = (35 * 2) + (50 * 2); // 70 + 100 = 170
        $this->assertEquals(170.00, $cost);
    }

    #[Test]
    public function it_calculates_cost_correctly_for_midnight_crossing()
    {
        // 11:00 PM to 1:00 AM (2 hours, crosses midnight)
        $cost = $this->service->calculateTotalCost($this->field, 2, '23:00');

        // Both hours should be night rate (11PM-12AM and 12AM-1AM)
        $this->assertEquals(100.00, $cost); // 50 * 2
    }

    #[Test]
    public function it_calculates_cost_correctly_starting_exactly_at_night_time()
    {
        // 6:00 PM to 8:00 PM (2 hours, starts exactly at night time)
        $cost = $this->service->calculateTotalCost($this->field, 2, '18:00');

        $this->assertEquals(100.00, $cost); // 50 * 2
    }

    #[Test]
    public function it_calculates_cost_correctly_for_early_morning_transition()
    {
        // 5:00 AM to 7:00 AM (2 hours, crosses 6AM day start)
        $cost = $this->service->calculateTotalCost($this->field, 2, '05:00');

        // 1 hour night rate (5AM-6AM) + 1 hour day rate (6AM-7AM)
        $expected = 50 + 35; // 85
        $this->assertEquals(85.00, $cost);
    }

    #[Test]
    public function it_handles_fields_without_night_rates()
    {
        $dayOnlyField = Field::factory()->create([
            'hourly_rate' => 40.00,
            'night_hourly_rate' => null,
            'night_time_start' => '18:00', // Required field, but night_hourly_rate is null
        ]);

        $cost = $this->service->calculateTotalCost($dayOnlyField, 4, '16:00');

        // Should use day rate for all hours when night_hourly_rate is null
        $this->assertEquals(160.00, $cost); // 40 * 4
    }

    #[Test]
    public function it_falls_back_to_simple_calculation_without_start_time()
    {
        $cost = $this->service->calculateTotalCost($this->field, 3);

        // Should use day rate for all hours when no start time provided
        $this->assertEquals(105.00, $cost); // 35 * 3
    }

    #[Test]
    public function night_time_detection_works_correctly()
    {
        // Test various times
        $this->assertFalse($this->field->isNightTime('17:00')); // Day
        $this->assertTrue($this->field->isNightTime('18:00'));  // Night start
        $this->assertTrue($this->field->isNightTime('23:00'));  // Night
        $this->assertTrue($this->field->isNightTime('00:00'));  // Midnight (night)
        $this->assertTrue($this->field->isNightTime('05:00'));  // Early morning (night)
        $this->assertFalse($this->field->isNightTime('06:00')); // Day start
        $this->assertFalse($this->field->isNightTime('12:00')); // Day
    }

    #[Test]
    public function ajax_endpoint_method_works_with_day_night_rates()
    {
        $estimate = $this->service->getReservationEstimate(
            $this->field->id,
            4,
            '16:00',
            []
        );

        $this->assertEquals(170.00, $estimate['total_cost']);
        $this->assertEquals($this->field->name, $estimate['field_name']);
        $this->assertEquals(4, $estimate['duration_hours']);
        $this->assertEquals('16:00', $estimate['start_time']);
    }

    #[Test]
    public function cost_calculation_with_utilities_preserves_day_night_logic()
    {
        // Create a utility for testing
        $utility = \App\Models\Utility::factory()->create([
            'hourly_rate' => 10.00,
        ]);

        $utilities = [['id' => $utility->id, 'hours' => 2]];

        $result = $this->service->calculateTotalCostWithUtilities(
            $this->field,
            4,
            '16:00',
            $utilities
        );

        // Field cost: 170 (day/night calculation)
        // Utility cost: 20 (10 * 2)
        // Total: 190
        $this->assertEquals(170.00, $result['field_cost']);
        $this->assertEquals(20.00, $result['utility_cost']);
        $this->assertEquals(190.00, $result['total_cost']);
    }
}
